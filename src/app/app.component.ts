import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import {
  DateRangeFilterComponent,
  DateRange,
} from './components/date-range-filter/date-range-filter.component';
import { OrgUtilizationChartComponent } from './components/org-utilization-chart/org-utilization-chart.component';
import { DeptUtilizationChartComponent } from './components/dept-utilization-chart/dept-utilization-chart.component';
import { LeaveStatusChartComponent } from './components/leave-status-chart/leave-status-chart.component';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    RouterOutlet,
    CommonModule,
    DateRangeFilterComponent,
    OrgUtilizationChartComponent,
    DeptUtilizationChartComponent,
    LeaveStatusChartComponent,
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css',
})
export class AppComponent {
  title = 'Employee Utilization Dashboard';
  currentDateRange: DateRange | null = null;

  onDateRangeChange(dateRange: DateRange) {
    this.currentDateRange = dateRange;
  }

  getCurrentDate(): string {
    return new Date().toLocaleDateString('en-US');
  }
}
