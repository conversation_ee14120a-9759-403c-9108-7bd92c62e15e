import { Component, EventEmitter, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { ButtonModule } from 'primeng/button';

export interface DateRange {
  startDate: Date;
  endDate: Date;
  label: string;
}

interface DateRangeOption {
  label: string;
  value: string;
}

@Component({
  selector: 'app-date-range-filter',
  standalone: true,
  imports: [CommonModule, FormsModule, DropdownModule, CalendarModule, ButtonModule],
  template: `
    <div class="flex flex-col sm:flex-row gap-4 items-center bg-white p-4 rounded-lg shadow-sm border">
      <div class="flex-1">
        <label class="block text-sm font-medium text-gray-700 mb-2">Time Period</label>
        <p-dropdown 
          [options]="dateRangeOptions" 
          [(ngModel)]="selectedOption"
          optionLabel="label" 
          optionValue="value"
          placeholder="Select time period"
          class="w-full"
          (onChange)="onOptionChange($event)">
        </p-dropdown>
      </div>
      
      <div class="flex-1" *ngIf="selectedOption === 'custom'">
        <label class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
        <p-calendar 
          [(ngModel)]="customStartDate"
          dateFormat="dd/mm/yy"
          placeholder="Select start date"
          class="w-full"
          (onSelect)="onCustomDateChange()">
        </p-calendar>
      </div>
      
      <div class="flex-1" *ngIf="selectedOption === 'custom'">
        <label class="block text-sm font-medium text-gray-700 mb-2">End Date</label>
        <p-calendar 
          [(ngModel)]="customEndDate"
          dateFormat="dd/mm/yy"
          placeholder="Select end date"
          class="w-full"
          (onSelect)="onCustomDateChange()">
        </p-calendar>
      </div>
      
      <div class="flex items-end">
        <p-button 
          label="Apply" 
          icon="pi pi-check"
          class="p-button-primary"
          (onClick)="applyDateRange()"
          [disabled]="!isValidDateRange()">
        </p-button>
      </div>
    </div>
    
    <div class="mt-2 text-sm text-gray-600" *ngIf="currentDateRange">
      <span class="font-medium">Current Period:</span> 
      {{ currentDateRange.label }} 
      ({{ formatDate(currentDateRange.startDate) }} - {{ formatDate(currentDateRange.endDate) }})
    </div>
  `,
  styles: [`
    :host {
      display: block;
      margin-bottom: 1.5rem;
    }
  `]
})
export class DateRangeFilterComponent {
  @Output() dateRangeChange = new EventEmitter<DateRange>();

  selectedOption: string = 'past7days';
  customStartDate: Date | null = null;
  customEndDate: Date | null = null;
  currentDateRange: DateRange | null = null;

  dateRangeOptions: DateRangeOption[] = [
    { label: 'Past 3 Days', value: 'past3days' },
    { label: 'Past 1 Week', value: 'past7days' },
    { label: 'Past 1 Month', value: 'past1month' },
    { label: 'Past 6 Months', value: 'past6months' },
    { label: 'Custom Date Range', value: 'custom' }
  ];

  ngOnInit() {
    // Set default date range (Past 1 Week)
    this.applyDateRange();
  }

  onOptionChange(event: any) {
    if (event.value !== 'custom') {
      this.customStartDate = null;
      this.customEndDate = null;
      this.applyDateRange();
    }
  }

  onCustomDateChange() {
    if (this.customStartDate && this.customEndDate) {
      this.applyDateRange();
    }
  }

  isValidDateRange(): boolean {
    if (this.selectedOption === 'custom') {
      return this.customStartDate !== null && 
             this.customEndDate !== null && 
             this.customStartDate <= this.customEndDate;
    }
    return true;
  }

  applyDateRange() {
    let startDate: Date;
    let endDate: Date = new Date();
    let label: string;

    switch (this.selectedOption) {
      case 'past3days':
        startDate = new Date();
        startDate.setDate(startDate.getDate() - 3);
        label = 'Past 3 Days';
        break;
      case 'past7days':
        startDate = new Date();
        startDate.setDate(startDate.getDate() - 7);
        label = 'Past 1 Week';
        break;
      case 'past1month':
        startDate = new Date();
        startDate.setMonth(startDate.getMonth() - 1);
        label = 'Past 1 Month';
        break;
      case 'past6months':
        startDate = new Date();
        startDate.setMonth(startDate.getMonth() - 6);
        label = 'Past 6 Months';
        break;
      case 'custom':
        if (!this.customStartDate || !this.customEndDate) {
          return;
        }
        startDate = new Date(this.customStartDate);
        endDate = new Date(this.customEndDate);
        label = 'Custom Range';
        break;
      default:
        startDate = new Date();
        startDate.setDate(startDate.getDate() - 7);
        label = 'Past 1 Week';
    }

    const dateRange: DateRange = {
      startDate,
      endDate,
      label
    };

    this.currentDateRange = dateRange;
    this.dateRangeChange.emit(dateRange);
  }

  formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }
}
