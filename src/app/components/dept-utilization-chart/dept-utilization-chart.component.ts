import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChartModule } from 'primeng/chart';
import { CardModule } from 'primeng/card';
import { EmployeeDataService, DepartmentUtilization } from '../../services/employee-data.service';
import { DateRange } from '../date-range-filter/date-range-filter.component';

@Component({
  selector: 'app-dept-utilization-chart',
  standalone: true,
  imports: [CommonModule, ChartModule, CardModule],
  template: `
    <p-card header="Department-wise Utilization" class="dashboard-card">
      <div class="chart-container">
        <p-chart 
          type="bar" 
          [data]="chartData" 
          [options]="chartOptions"
          class="w-full h-full">
        </p-chart>
      </div>
      
      <div class="mt-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
          <div 
            *ngFor="let dept of departmentSummary" 
            class="bg-gray-50 p-3 rounded-lg border">
            <div class="font-semibold text-sm text-gray-800 mb-2">{{ dept.department }}</div>
            <div class="flex justify-between text-xs">
              <span class="text-green-600">B: {{ dept.billable }}</span>
              <span class="text-yellow-600">PB: {{ dept.partiallyBillable }}</span>
              <span class="text-red-600">NB: {{ dept.nonBillable }}</span>
            </div>
            <div class="mt-1 text-xs text-gray-600">
              Total: {{ dept.billable + dept.partiallyBillable + dept.nonBillable }}
            </div>
          </div>
        </div>
      </div>
    </p-card>
  `,
  styles: [`
    :host {
      display: block;
    }
  `]
})
export class DeptUtilizationChartComponent implements OnChanges {
  @Input() dateRange: DateRange | null = null;

  chartData: any = {};
  chartOptions: any = {};
  departmentSummary: DepartmentUtilization[] = [];

  constructor(private employeeDataService: EmployeeDataService) {
    this.initializeChartOptions();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['dateRange'] && this.dateRange) {
      this.loadChartData();
    }
  }

  private initializeChartOptions() {
    this.chartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20
          }
        },
        tooltip: {
          callbacks: {
            title: (context: any) => {
              return `${context[0].label} Department`;
            },
            label: (context: any) => {
              const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
              const percentage = ((context.parsed.y / total) * 100).toFixed(1);
              return `${context.dataset.label}: ${context.parsed.y} employees (${percentage}%)`;
            }
          }
        }
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Departments'
          },
          grid: {
            display: false
          }
        },
        y: {
          display: true,
          title: {
            display: true,
            text: 'Number of Employees'
          },
          beginAtZero: true,
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          }
        }
      },
      interaction: {
        mode: 'index',
        intersect: false
      }
    };
  }

  private loadChartData() {
    if (!this.dateRange) return;

    this.employeeDataService.getDepartmentUtilization(
      this.dateRange.startDate, 
      this.dateRange.endDate
    ).subscribe((data: DepartmentUtilization[]) => {
      this.updateChart(data);
      this.departmentSummary = data.sort((a, b) => 
        (b.billable + b.partiallyBillable + b.nonBillable) - 
        (a.billable + a.partiallyBillable + a.nonBillable)
      );
    });
  }

  private updateChart(data: DepartmentUtilization[]) {
    const labels = data.map(item => item.department);

    this.chartData = {
      labels: labels,
      datasets: [
        {
          label: 'Billable',
          data: data.map(item => item.billable),
          backgroundColor: '#10b981',
          borderColor: '#059669',
          borderWidth: 1,
          borderRadius: 4,
          borderSkipped: false
        },
        {
          label: 'Partially Billable',
          data: data.map(item => item.partiallyBillable),
          backgroundColor: '#f59e0b',
          borderColor: '#d97706',
          borderWidth: 1,
          borderRadius: 4,
          borderSkipped: false
        },
        {
          label: 'Non-Billable',
          data: data.map(item => item.nonBillable),
          backgroundColor: '#ef4444',
          borderColor: '#dc2626',
          borderWidth: 1,
          borderRadius: 4,
          borderSkipped: false
        }
      ]
    };
  }
}
