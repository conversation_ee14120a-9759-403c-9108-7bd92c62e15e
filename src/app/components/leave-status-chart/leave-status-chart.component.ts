import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChartModule } from 'primeng/chart';
import { CardModule } from 'primeng/card';
import { EmployeeDataService, LeaveData } from '../../services/employee-data.service';
import { DateRange } from '../date-range-filter/date-range-filter.component';

@Component({
  selector: 'app-leave-status-chart',
  standalone: true,
  imports: [CommonModule, ChartModule, CardModule],
  template: `
    <p-card header="Employee Leave Status" class="dashboard-card">
      <div class="chart-container">
        <p-chart 
          type="doughnut" 
          [data]="chartData" 
          [options]="chartOptions"
          class="w-full h-full">
        </p-chart>
      </div>
      
      <div class="mt-4 grid grid-cols-2 gap-4">
        <div class="bg-blue-50 p-4 rounded-lg text-center">
          <div class="text-3xl font-bold text-blue-600">{{ totalFullDay }}</div>
          <div class="text-sm text-blue-700 mt-1">Full Day Leaves</div>
          <div class="text-xs text-blue-600 mt-1">
            {{ fullDayPercentage }}% of total leaves
          </div>
        </div>
        <div class="bg-indigo-50 p-4 rounded-lg text-center">
          <div class="text-3xl font-bold text-indigo-600">{{ totalHalfDay }}</div>
          <div class="text-sm text-indigo-700 mt-1">Half Day Leaves</div>
          <div class="text-xs text-indigo-600 mt-1">
            {{ halfDayPercentage }}% of total leaves
          </div>
        </div>
      </div>
      
      <div class="mt-4 bg-gray-50 p-3 rounded-lg">
        <div class="text-sm font-medium text-gray-700 mb-2">Leave Trends</div>
        <div class="grid grid-cols-2 gap-4 text-xs">
          <div>
            <span class="text-gray-600">Average Full Day/Day:</span>
            <span class="font-medium ml-1">{{ avgFullDayPerDay }}</span>
          </div>
          <div>
            <span class="text-gray-600">Average Half Day/Day:</span>
            <span class="font-medium ml-1">{{ avgHalfDayPerDay }}</span>
          </div>
          <div>
            <span class="text-gray-600">Peak Full Day:</span>
            <span class="font-medium ml-1">{{ peakFullDay }}</span>
          </div>
          <div>
            <span class="text-gray-600">Peak Half Day:</span>
            <span class="font-medium ml-1">{{ peakHalfDay }}</span>
          </div>
        </div>
      </div>
    </p-card>
  `,
  styles: [`
    :host {
      display: block;
    }
  `]
})
export class LeaveStatusChartComponent implements OnChanges {
  @Input() dateRange: DateRange | null = null;

  chartData: any = {};
  chartOptions: any = {};
  totalFullDay: number = 0;
  totalHalfDay: number = 0;
  fullDayPercentage: number = 0;
  halfDayPercentage: number = 0;
  avgFullDayPerDay: number = 0;
  avgHalfDayPerDay: number = 0;
  peakFullDay: number = 0;
  peakHalfDay: number = 0;

  constructor(private employeeDataService: EmployeeDataService) {
    this.initializeChartOptions();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['dateRange'] && this.dateRange) {
      this.loadChartData();
    }
  }

  private initializeChartOptions() {
    this.chartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            usePointStyle: true,
            padding: 20,
            font: {
              size: 12
            }
          }
        },
        tooltip: {
          callbacks: {
            label: (context: any) => {
              const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
              const percentage = ((context.parsed / total) * 100).toFixed(1);
              return `${context.label}: ${context.parsed} employees (${percentage}%)`;
            }
          }
        }
      },
      cutout: '60%',
      elements: {
        arc: {
          borderWidth: 2,
          borderColor: '#ffffff'
        }
      }
    };
  }

  private loadChartData() {
    if (!this.dateRange) return;

    this.employeeDataService.getLeaveData(
      this.dateRange.startDate, 
      this.dateRange.endDate
    ).subscribe((data: LeaveData[]) => {
      this.updateChart(data);
      this.calculateStatistics(data);
    });
  }

  private updateChart(data: LeaveData[]) {
    this.totalFullDay = data.reduce((sum, item) => sum + item.fullDay, 0);
    this.totalHalfDay = data.reduce((sum, item) => sum + item.halfDay, 0);

    const total = this.totalFullDay + this.totalHalfDay;
    this.fullDayPercentage = total > 0 ? Math.round((this.totalFullDay / total) * 100) : 0;
    this.halfDayPercentage = total > 0 ? Math.round((this.totalHalfDay / total) * 100) : 0;

    this.chartData = {
      labels: ['Full Day Leaves', 'Half Day Leaves'],
      datasets: [
        {
          data: [this.totalFullDay, this.totalHalfDay],
          backgroundColor: [
            '#3b82f6', // Blue for Full Day
            '#6366f1'  // Indigo for Half Day
          ],
          borderColor: [
            '#2563eb',
            '#4f46e5'
          ],
          borderWidth: 2,
          hoverBackgroundColor: [
            '#2563eb',
            '#4f46e5'
          ],
          hoverBorderColor: [
            '#1d4ed8',
            '#4338ca'
          ]
        }
      ]
    };
  }

  private calculateStatistics(data: LeaveData[]) {
    if (data.length === 0) {
      this.avgFullDayPerDay = 0;
      this.avgHalfDayPerDay = 0;
      this.peakFullDay = 0;
      this.peakHalfDay = 0;
      return;
    }

    // Calculate averages
    this.avgFullDayPerDay = Math.round(
      (data.reduce((sum, item) => sum + item.fullDay, 0) / data.length) * 10
    ) / 10;
    
    this.avgHalfDayPerDay = Math.round(
      (data.reduce((sum, item) => sum + item.halfDay, 0) / data.length) * 10
    ) / 10;

    // Calculate peaks
    this.peakFullDay = Math.max(...data.map(item => item.fullDay));
    this.peakHalfDay = Math.max(...data.map(item => item.halfDay));
  }
}
