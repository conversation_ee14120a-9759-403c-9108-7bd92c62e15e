import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChartModule } from 'primeng/chart';
import { CardModule } from 'primeng/card';
import { EmployeeDataService, UtilizationData } from '../../services/employee-data.service';
import { DateRange } from '../date-range-filter/date-range-filter.component';

@Component({
  selector: 'app-org-utilization-chart',
  standalone: true,
  imports: [CommonModule, ChartModule, CardModule],
  template: `
    <p-card header="Organizational Level Utilization" class="dashboard-card">
      <div class="chart-container">
        <p-chart 
          type="line" 
          [data]="chartData" 
          [options]="chartOptions"
          class="w-full h-full">
        </p-chart>
      </div>
      
      <div class="mt-4 grid grid-cols-3 gap-4 text-center">
        <div class="bg-green-50 p-3 rounded-lg">
          <div class="text-2xl font-bold text-green-600">{{ totalBillable }}</div>
          <div class="text-sm text-green-700">Billable</div>
        </div>
        <div class="bg-yellow-50 p-3 rounded-lg">
          <div class="text-2xl font-bold text-yellow-600">{{ totalPartiallyBillable }}</div>
          <div class="text-sm text-yellow-700">Partially Billable</div>
        </div>
        <div class="bg-red-50 p-3 rounded-lg">
          <div class="text-2xl font-bold text-red-600">{{ totalNonBillable }}</div>
          <div class="text-sm text-red-700">Non-Billable</div>
        </div>
      </div>
    </p-card>
  `,
  styles: [`
    :host {
      display: block;
    }
  `]
})
export class OrgUtilizationChartComponent implements OnChanges {
  @Input() dateRange: DateRange | null = null;

  chartData: any = {};
  chartOptions: any = {};
  totalBillable: number = 0;
  totalPartiallyBillable: number = 0;
  totalNonBillable: number = 0;

  constructor(private employeeDataService: EmployeeDataService) {
    this.initializeChartOptions();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['dateRange'] && this.dateRange) {
      this.loadChartData();
    }
  }

  private initializeChartOptions() {
    this.chartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20
          }
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          callbacks: {
            title: (context: any) => {
              const date = new Date(context[0].label);
              return date.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              });
            },
            label: (context: any) => {
              return `${context.dataset.label}: ${context.parsed.y} employees`;
            }
          }
        }
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Date'
          },
          grid: {
            display: false
          }
        },
        y: {
          display: true,
          title: {
            display: true,
            text: 'Number of Employees'
          },
          beginAtZero: true,
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          }
        }
      },
      interaction: {
        mode: 'nearest',
        axis: 'x',
        intersect: false
      }
    };
  }

  private loadChartData() {
    if (!this.dateRange) return;

    this.employeeDataService.getUtilizationData(
      this.dateRange.startDate, 
      this.dateRange.endDate
    ).subscribe((data: UtilizationData[]) => {
      this.updateChart(data);
      this.calculateTotals(data);
    });
  }

  private updateChart(data: UtilizationData[]) {
    const labels = data.map(item => 
      item.date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      })
    );

    this.chartData = {
      labels: labels,
      datasets: [
        {
          label: 'Billable',
          data: data.map(item => item.billable),
          borderColor: '#10b981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          fill: true,
          tension: 0.4,
          pointBackgroundColor: '#10b981',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointRadius: 4
        },
        {
          label: 'Partially Billable',
          data: data.map(item => item.partiallyBillable),
          borderColor: '#f59e0b',
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          fill: true,
          tension: 0.4,
          pointBackgroundColor: '#f59e0b',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointRadius: 4
        },
        {
          label: 'Non-Billable',
          data: data.map(item => item.nonBillable),
          borderColor: '#ef4444',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          fill: true,
          tension: 0.4,
          pointBackgroundColor: '#ef4444',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointRadius: 4
        }
      ]
    };
  }

  private calculateTotals(data: UtilizationData[]) {
    if (data.length === 0) {
      this.totalBillable = 0;
      this.totalPartiallyBillable = 0;
      this.totalNonBillable = 0;
      return;
    }

    // Calculate average for the period
    this.totalBillable = Math.round(
      data.reduce((sum, item) => sum + item.billable, 0) / data.length
    );
    this.totalPartiallyBillable = Math.round(
      data.reduce((sum, item) => sum + item.partiallyBillable, 0) / data.length
    );
    this.totalNonBillable = Math.round(
      data.reduce((sum, item) => sum + item.nonBillable, 0) / data.length
    );
  }
}
