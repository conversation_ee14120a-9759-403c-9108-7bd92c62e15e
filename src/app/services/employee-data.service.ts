import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';

export interface Employee {
  id: number;
  name: string;
  department: string;
  billableStatus: 'Billable' | 'Partially Billable' | 'Non-Billable';
  leaveStatus: 'Full Day' | 'Half Day' | 'Present';
  date: Date;
  team: string;
}

export interface UtilizationData {
  date: Date;
  billable: number;
  partiallyBillable: number;
  nonBillable: number;
}

export interface DepartmentUtilization {
  department: string;
  billable: number;
  partiallyBillable: number;
  nonBillable: number;
}

export interface LeaveData {
  date: Date;
  fullDay: number;
  halfDay: number;
}

@Injectable({
  providedIn: 'root'
})
export class EmployeeDataService {
  private departments = [
    'Engineering', 'Product', 'Design', 'Marketing', 
    'Sales', 'Finance', 'Operations', 'Legal'
  ];

  private supportTeams = ['HR', 'IT', 'Admin', 'Facilities'];

  constructor() { }

  private generateMockEmployees(startDate: Date, endDate: Date): Employee[] {
    const employees: Employee[] = [];
    const totalEmployees = 150;
    
    for (let empId = 1; empId <= totalEmployees; empId++) {
      const currentDate = new Date(startDate);
      
      while (currentDate <= endDate) {
        const department = this.departments[Math.floor(Math.random() * this.departments.length)];
        const billableStatuses: ('Billable' | 'Partially Billable' | 'Non-Billable')[] = 
          ['Billable', 'Partially Billable', 'Non-Billable'];
        const leaveStatuses: ('Full Day' | 'Half Day' | 'Present')[] = 
          ['Full Day', 'Half Day', 'Present'];
        
        // Higher probability for Present status
        const leaveWeights = [0.05, 0.1, 0.85]; // 5% Full Day, 10% Half Day, 85% Present
        const leaveRandom = Math.random();
        let leaveStatus: 'Full Day' | 'Half Day' | 'Present' = 'Present';
        
        if (leaveRandom < leaveWeights[0]) {
          leaveStatus = 'Full Day';
        } else if (leaveRandom < leaveWeights[0] + leaveWeights[1]) {
          leaveStatus = 'Half Day';
        }
        
        // Billable status distribution
        const billableWeights = [0.6, 0.25, 0.15]; // 60% Billable, 25% Partially, 15% Non-billable
        const billableRandom = Math.random();
        let billableStatus: 'Billable' | 'Partially Billable' | 'Non-Billable' = 'Billable';
        
        if (billableRandom < billableWeights[0]) {
          billableStatus = 'Billable';
        } else if (billableRandom < billableWeights[0] + billableWeights[1]) {
          billableStatus = 'Partially Billable';
        } else {
          billableStatus = 'Non-Billable';
        }

        employees.push({
          id: empId,
          name: `Employee ${empId}`,
          department,
          billableStatus,
          leaveStatus,
          date: new Date(currentDate),
          team: department
        });
        
        currentDate.setDate(currentDate.getDate() + 1);
      }
    }
    
    return employees;
  }

  getUtilizationData(startDate: Date, endDate: Date): Observable<UtilizationData[]> {
    const employees = this.generateMockEmployees(startDate, endDate);
    const utilizationMap = new Map<string, UtilizationData>();
    
    employees.forEach(emp => {
      // Exclude support teams
      if (this.supportTeams.includes(emp.department)) {
        return;
      }
      
      const dateKey = emp.date.toDateString();
      if (!utilizationMap.has(dateKey)) {
        utilizationMap.set(dateKey, {
          date: emp.date,
          billable: 0,
          partiallyBillable: 0,
          nonBillable: 0
        });
      }
      
      const data = utilizationMap.get(dateKey)!;
      switch (emp.billableStatus) {
        case 'Billable':
          data.billable++;
          break;
        case 'Partially Billable':
          data.partiallyBillable++;
          break;
        case 'Non-Billable':
          data.nonBillable++;
          break;
      }
    });
    
    return of(Array.from(utilizationMap.values()).sort((a, b) => a.date.getTime() - b.date.getTime()));
  }

  getDepartmentUtilization(startDate: Date, endDate: Date): Observable<DepartmentUtilization[]> {
    const employees = this.generateMockEmployees(startDate, endDate);
    const deptMap = new Map<string, DepartmentUtilization>();
    
    employees.forEach(emp => {
      // Exclude support teams
      if (this.supportTeams.includes(emp.department)) {
        return;
      }
      
      if (!deptMap.has(emp.department)) {
        deptMap.set(emp.department, {
          department: emp.department,
          billable: 0,
          partiallyBillable: 0,
          nonBillable: 0
        });
      }
      
      const data = deptMap.get(emp.department)!;
      switch (emp.billableStatus) {
        case 'Billable':
          data.billable++;
          break;
        case 'Partially Billable':
          data.partiallyBillable++;
          break;
        case 'Non-Billable':
          data.nonBillable++;
          break;
      }
    });
    
    return of(Array.from(deptMap.values()));
  }

  getLeaveData(startDate: Date, endDate: Date): Observable<LeaveData[]> {
    const employees = this.generateMockEmployees(startDate, endDate);
    const leaveMap = new Map<string, LeaveData>();
    
    employees.forEach(emp => {
      const dateKey = emp.date.toDateString();
      if (!leaveMap.has(dateKey)) {
        leaveMap.set(dateKey, {
          date: emp.date,
          fullDay: 0,
          halfDay: 0
        });
      }
      
      const data = leaveMap.get(dateKey)!;
      switch (emp.leaveStatus) {
        case 'Full Day':
          data.fullDay++;
          break;
        case 'Half Day':
          data.halfDay++;
          break;
      }
    });
    
    return of(Array.from(leaveMap.values()).sort((a, b) => a.date.getTime() - b.date.getTime()));
  }
}
